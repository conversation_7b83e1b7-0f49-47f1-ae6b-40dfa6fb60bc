import { <PERSON><PERSON>, type CommandContext, Context } from "grammy"
import { log } from "./log"
import { Content } from "./content"
import { User, Wallet, TradingHistory, Fees } from "./models"
import { getChainType, getChainId, generateMockAddress, format<PERSON>hain<PERSON>ame, getCurrencySymbol, generateMockSeed } from "./tools/shared"
import { dataChainList, dataChainName, type TypeChainName } from "./data"

const content = new Content()

/**
 * Telegram bot class for the trading bot
 *
 * This class handles all Telegram bot interactions, including user commands
 * for wallet management, trading operations, and system information.
 * It integrates with the PostgreSQL database through the model layer.
 */
export class Telegram {
  public bot = new Bot(process.env.TELEGRAM_TOKEN || ``)

  /**
   * Reusable helper method to reply with content
   * @param ctx Telegram context object
   * @param contentKey Content key to retrieve from content system
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise from ctx.reply()
   */
  private async reply(ctx: CommandContext<Context>, contentKey: string, data?: Record<string, any>, options?: any): Promise<any> {
    return ctx.reply(content.get(contentKey, data), { parse_mode: `MarkdownV2`, ...options })
  }

  constructor() {
    // Register basic commands
    this.bot.command(`start`, this.handleCommandStart.bind(this))
    this.bot.command(`help`, this.handleCommandHelp.bind(this))

    // Wallet management commands
    this.bot.command(`createwallet`, this.handleCommandCreateWallet.bind(this))
    this.bot.command(`createwallets`, this.handleCommandCreateMultipleWallets.bind(this))
    this.bot.command(`wallets`, this.handleCommandListWallets.bind(this))
    this.bot.command(`wallet`, this.handleCommandWalletInfo.bind(this))
    this.bot.command(`importwallet`, this.handleCommandImportWallet.bind(this))
    this.bot.command(`deletewallet`, this.handleCommandDeleteWallet.bind(this))
    this.bot.command(`balance`, this.handleCommandBalance.bind(this))

    // Trading and history commands
    this.bot.command(`history`, this.handleCommandTradingHistory.bind(this))
    this.bot.command(`stats`, this.handleCommandStats.bind(this))

    // Fee and configuration commands
    this.bot.command(`fees`, this.handleCommandFees.bind(this))
    this.bot.command(`chains`, this.handleCommandChains.bind(this))

    // Handled error
    this.bot.catch((err) => {
      log.error(JSON.stringify(err, null, 2))
    })

    // Started bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle the /start command
   * @param ctx Command context from Grammy
   */
  public async handleCommandStart(ctx: CommandContext<Context>): Promise<void> {
    await this.reply(ctx, "start")
  }

  /**
   * Handle the /help command
   * @param ctx Command context from Grammy
   */
  public async handleCommandHelp(ctx: CommandContext<Context>): Promise<void> {
    await this.reply(ctx, "help")
  }

  /**
   * Handle the /createwallet command
   * Format: /createwallet <name>
   */
  public async handleCommandCreateWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "createwallet_usage")
        return
      }

      const walletName = args[0] as string

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Use default chain if not provided (for backward compatibility)
      const chain = `solana_mainnet`
      const address = generateMockAddress(chain)
      const chainId = getChainId(chain)
      const seed = generateMockSeed()

      // Create wallet
      const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, seed, "system")

      if (!wallet) {
        await this.reply(ctx, "createwallet_failed", { walletName })
        return
      }

      await this.reply(ctx, "createwallet_success_new", {
        walletName,
        chain: formatChainName(chain),
        address
      })
    } catch (error) {
      log.error(`Error in handleCommandCreateWallet: ${error}`)
      await this.reply(ctx, "createwallet_error")
    }
  }

  /**
   * Handle the /wallets command to list all wallets
   */
  public async handleCommandListWallets(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get user
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "wallets_no_user")
        return
      }

      // Get all wallets for user
      const wallets = await Wallet.getAllForUser(user.id)

      if (wallets.length === 0) {
        await this.reply(ctx, "wallets_empty")
        return
      }

      // Format wallet list
      const walletList = wallets
        .map((wallet, index) => {
          const balance = wallet.balance.toString()
          const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
          const chainName = formatChainName(wallet.chain as TypeChainName)
          return `${index + 1}. ${wallet.name}\n   🔗 ${chainName}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
        })
        .join("\n\n")

      await this.reply(ctx, "wallets_list", { walletList })
    } catch (error) {
      log.error(`Error in handleCommandListWallets: ${error}`)
      await this.reply(ctx, "wallets_error")
    }
  }

  /**
   * Handle the /importwallet command
   * Format: /importwallet <name> <seed>
   */
  public async handleCommandImportWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 2) {
        await this.reply(ctx, "importwallet_usage")
        return
      }

      const walletName = args[0] as string
      const seed = args[1] as string

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Use default chain for backward compatibility
      const chain = `solana_mainnet`
      const address = generateMockAddress(chain)
      const chainId = getChainId(chain)

      // Import wallet
      const wallet = await Wallet.import(user.id, walletName, chain, chainId, address, seed)

      if (!wallet) {
        await this.reply(ctx, "importwallet_failed", { walletName })
        return
      }

      await this.reply(ctx, "importwallet_success_new", {
        walletName,
        chain: formatChainName(chain),
        address
      })
    } catch (error) {
      log.error(`Error in handleCommandImportWallet: ${error}`)
      await this.reply(ctx, "importwallet_error")
    }
  }

  /**
   * Handle the /deletewallet command
   * Format: /deletewallet <name>
   */
  public async handleCommandDeleteWallet(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "deletewallet_usage")
        return
      }

      const walletName = args[0] as string

      // Get user
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "deletewallet_no_wallets")
        return
      }

      // Find wallet by name
      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "deletewallet_not_found", { walletName })
        return
      }

      // Delete wallet
      const success = await Wallet.delete(wallet.id, user.id)

      if (!success) {
        await this.reply(ctx, "deletewallet_failed", { walletName })
        return
      }

      await this.reply(ctx, "deletewallet_success", { walletName })
    } catch (error) {
      log.error(`Error in handleCommandDeleteWallet: ${error}`)
      await this.reply(ctx, "deletewallet_error")
    }
  }

  /**
   * Handle the /createwallets command to create multiple wallets at once
   * Format: /createwallets <count> <prefix>
   */
  public async handleCommandCreateMultipleWallets(ctx: CommandContext<Context>): Promise<void> {
    try {
      // Get command arguments
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 2) {
        await this.reply(ctx, "createwallets_usage")
        return
      }

      const count = parseInt(args[0] as string, 10)
      const prefix = args[1] as string

      if (isNaN(count) || count <= 0 || count > 100) {
        await this.reply(ctx, "createwallets_count_invalid")
        return
      }

      // Get or create user
      const telegramId = ctx.from?.id || 0
      const user = await User.getOrCreate(telegramId, ctx.from?.username || `user_${telegramId}`, ctx.from?.first_name || "Unknown")

      if (!user) {
        await this.reply(ctx, "createwallet_failed_user")
        return
      }

      // Create wallets
      const createdWallets = []
      const failedWallets = []

      // Use default chain for batch creation
      const chain = `solana_mainnet`
      const chainId = getChainId(chain)

      for (let i = 1; i <= count; i++) {
        const walletName = `${prefix}${i}`
        const address = generateMockAddress(chain)
        const seed = generateMockSeed()

        const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, seed, "system")

        if (wallet) {
          createdWallets.push(walletName)
        } else {
          failedWallets.push(walletName)
        }
      }

      // Build result message using content system
      const successMessage =
        createdWallets.length > 0
          ? content.get("createwallets_success", {
              count: createdWallets.length,
              walletList: createdWallets.join(", ")
            })
          : ""

      const failureMessage =
        failedWallets.length > 0
          ? content.get("createwallets_failed", {
              count: failedWallets.length,
              walletList: failedWallets.join(", ")
            })
          : ""

      const message = [successMessage, failureMessage].filter((msg) => msg).join("\n\n")

      await this.reply(ctx, "createwallets_result", { message })
    } catch (error) {
      log.error(`Error in handleCommandCreateMultipleWallets: ${error}`)
      await this.reply(ctx, "createwallets_error")
    }
  }

  /**
   * Handle the /wallet command to show detailed wallet information
   * Format: /wallet <name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandWalletInfo(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "wallet_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const chainName = formatChainName(wallet.chain as TypeChainName)
      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
      const balance = wallet.balance.toString()
      const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

      await this.reply(ctx, "wallet_info", {
        name: wallet.name,
        chainName,
        address: wallet.address,
        balance,
        symbol,
        chainId: wallet.chainId,
        createdDate,
        createdBy: wallet.createdBy
      })
    } catch (error) {
      log.error(`Error in handleCommandWalletInfo: ${error}`)
      await this.reply(ctx, "wallet_info_error")
    }
  }

  /**
   * Handle the /balance command to show wallet balance
   * Format: /balance <name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandBalance(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "balance_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)
      const balance = wallet.balance.toString()
      const chainName = formatChainName(wallet.chain as TypeChainName)

      await this.reply(ctx, "balance_display", {
        walletName: wallet.name,
        balance,
        symbol,
        chainName
      })
    } catch (error) {
      log.error(`Error in handleCommandBalance: ${error}`)
      await this.reply(ctx, "balance_error")
    }
  }

  /**
   * Handle the /history command to show trading history
   * Format: /history <wallet_name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandTradingHistory(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "history_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const history = await TradingHistory.getForWallet(BigInt(wallet.id), 10)

      if (history.length === 0) {
        await this.reply(ctx, "history_empty", { walletName })
        return
      }

      const historyText = history
        .map((trade, index) => {
          const operation = trade.operation === "B" ? "🟢 BUY" : "🔴 SELL"
          const status = trade.success ? "✅" : "❌"
          const amount = trade.amount.toString()
          const symbol = getCurrencySymbol(trade.chain as TypeChainName)
          const date = trade.createdAt?.toLocaleDateString() || "Unknown"

          return `${index + 1}. ${operation} ${status}\n   💰 ${amount} ${symbol}\n   📅 ${date}`
        })
        .join("\n\n")

      await this.reply(ctx, "history_display", { walletName, historyText })
    } catch (error) {
      log.error(`Error in handleCommandTradingHistory: ${error}`)
      await this.reply(ctx, "history_error")
    }
  }

  /**
   * Handle the /stats command to show trading statistics
   * Format: /stats <wallet_name>
   * @param ctx Command context from Grammy
   */
  public async handleCommandStats(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "stats_usage")
        return
      }

      const walletName = args[0] as string
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await this.reply(ctx, "user_not_found")
        return
      }

      const wallet = await Wallet.getByName(user.id, walletName)

      if (!wallet) {
        await this.reply(ctx, "wallet_not_found", { walletName })
        return
      }

      const stats = await TradingHistory.getWalletStats(BigInt(wallet.id))

      if (!stats) {
        await this.reply(ctx, "stats_empty", { walletName })
        return
      }

      const successRate = stats.totalTrades > 0 ? ((stats.successTrades / stats.totalTrades) * 100).toFixed(2) : "0"
      const symbol = getCurrencySymbol(wallet.chain as TypeChainName)

      await this.reply(ctx, "stats_display", {
        walletName,
        totalTrades: stats.totalTrades,
        successTrades: stats.successTrades,
        failedTrades: stats.failedTrades,
        successRate,
        totalVolume: stats.totalVolume.toString(),
        successVolume: stats.successVolume.toString(),
        symbol,
        buyTrades: stats.buyTrades,
        sellTrades: stats.sellTrades
      })
    } catch (error) {
      log.error(`Error in handleCommandStats: ${error}`)
      await this.reply(ctx, "stats_error")
    }
  }

  /**
   * Handle the /fees command to show fee configuration
   * Format: /fees <chain>
   * @param ctx Command context from Grammy
   */
  public async handleCommandFees(ctx: CommandContext<Context>): Promise<void> {
    try {
      const args = ctx.match.split(" ").filter((arg) => arg.trim() !== "")

      if (args.length < 1) {
        await this.reply(ctx, "fees_usage")
        return
      }

      const chainInput = args[0] as string

      if (chainInput.toLowerCase() === "all") {
        const allFees = await Fees.getAll()

        if (allFees.length === 0) {
          await this.reply(ctx, "fees_empty")
          return
        }

        const feesList = allFees
          .map((fee, index) => {
            const chainName = formatChainName(fee.chain as TypeChainName)
            return `${index + 1}. ${chainName}\n   💰 Fee: ${fee.fee}%\n   📍 Receiver: \`${fee.receiver}\``
          })
          .join("\n\n")

        await this.reply(ctx, "fees_all", { feesList })
        return
      }

      const chain = getChainType(chainInput)
      if (!chain) {
        await this.reply(ctx, "chain_invalid")
        return
      }

      const fees = await Fees.getByChain(chain)

      if (fees.length === 0) {
        await this.reply(ctx, "fees_chain_empty", { chainName: formatChainName(chain) })
        return
      }

      const chainName = formatChainName(chain)
      const feesList = fees
        .map((fee, index) => {
          return `${index + 1}. Chain ID: ${fee.chainId}\n   💰 Fee: ${fee.fee}%\n   📍 Receiver: \`${fee.receiver}\``
        })
        .join("\n\n")

      await this.reply(ctx, "fees_chain_display", { chainName, feesList })
    } catch (error) {
      log.error(`Error in handleCommandFees: ${error}`)
      await this.reply(ctx, "fees_error")
    }
  }

  /**
   * Handle the /chains command to show supported blockchain networks
   * @param ctx Command context from Grammy
   */
  public async handleCommandChains(ctx: CommandContext<Context>): Promise<void> {
    try {
      const chainsList = dataChainName
        .map((chain, index) => {
          const chainName = formatChainName(chain)
          const symbol = getCurrencySymbol(chain)
          const chainId = getChainId(chain)
          return `${index + 1}. ${chainName}\n   🔗 ID: ${chainId}\n   💰 Symbol: ${symbol}\n   📝 Code: \`${chain}\``
        })
        .join("\n\n")

      await this.reply(ctx, "chains_display", { chainsList })
    } catch (error) {
      log.error(`Error in handleCommandChains: ${error}`)
      await this.reply(ctx, "chains_error")
    }
  }
}
